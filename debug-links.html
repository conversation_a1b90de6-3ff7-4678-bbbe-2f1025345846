<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>链接调试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .debug-info { background: #f0f0f0; padding: 15px; margin: 10px 0; border-radius: 5px; }
        .button-info { background: #e8f4fd; padding: 10px; margin: 5px 0; border-left: 4px solid #2196F3; }
        .error { background: #ffebee; border-left: 4px solid #f44336; }
        .success { background: #e8f5e8; border-left: 4px solid #4caf50; }
    </style>
</head>
<body>
    <h1>链接调试工具</h1>
    
    <div class="debug-info">
        <h3>当前页面信息</h3>
        <p>URL: <span id="currentUrl"></span></p>
        <p>时间: <span id="currentTime"></span></p>
    </div>
    
    <div class="debug-info">
        <h3>检查主页按钮</h3>
        <button onclick="checkMainPageButtons()" style="padding: 10px 20px; background: #2196F3; color: white; border: none; border-radius: 5px; cursor: pointer;">检查主页按钮</button>
        <div id="buttonResults"></div>
    </div>
    
    <div class="debug-info">
        <h3>测试链接</h3>
        <div>
            <a href="admissions.html" onclick="logClick(this)">招生信息</a> |
            <a href="programs.html" onclick="logClick(this)">专业课程</a> |
            <a href="international.html" onclick="logClick(this)">国际学生</a> |
            <a href="facilities.html" onclick="logClick(this)">校园设施</a>
        </div>
        <div id="clickLog"></div>
    </div>
    
    <script>
        document.getElementById('currentUrl').textContent = window.location.href;
        document.getElementById('currentTime').textContent = new Date().toLocaleString();
        
        function logClick(element) {
            const log = document.getElementById('clickLog');
            const time = new Date().toLocaleTimeString();
            log.innerHTML += `<div class="button-info">[${time}] 点击: ${element.textContent} → ${element.href}</div>`;
        }
        
        function checkMainPageButtons() {
            const results = document.getElementById('buttonResults');
            results.innerHTML = '<p>正在检查主页...</p>';
            
            // 使用fetch检查主页内容
            fetch('index.html')
                .then(response => response.text())
                .then(html => {
                    const parser = new DOMParser();
                    const doc = parser.parseFromString(html, 'text/html');
                    
                    // 查找所有轮播图按钮
                    const heroButtons = doc.querySelectorAll('.hero-buttons a');
                    let resultHtml = '<h4>找到的按钮:</h4>';
                    
                    heroButtons.forEach((button, index) => {
                        const href = button.getAttribute('href');
                        const text = button.textContent.trim();
                        const slideIndex = Math.floor(index / 2) + 1;
                        const buttonIndex = (index % 2) + 1;
                        
                        resultHtml += `<div class="button-info">
                            轮播图 ${slideIndex}, 按钮 ${buttonIndex}: "${text}" → ${href}
                        </div>`;
                    });
                    
                    results.innerHTML = resultHtml;
                })
                .catch(error => {
                    results.innerHTML = `<div class="error">错误: ${error.message}</div>`;
                });
        }
        
        // 页面加载时自动检查
        window.onload = function() {
            checkMainPageButtons();
        };
    </script>
</body>
</html>
