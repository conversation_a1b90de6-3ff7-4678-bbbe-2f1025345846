<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>导航修复工具</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', <PERSON>l, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 900px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 30px;
            font-size: 2.5em;
        }
        .section {
            margin-bottom: 30px;
            padding: 20px;
            border-radius: 10px;
            background: #f8f9fa;
            border-left: 4px solid #007bff;
        }
        .test-btn {
            display: inline-block;
            margin: 8px;
            padding: 12px 20px;
            background: #007bff;
            color: white;
            text-decoration: none;
            border-radius: 25px;
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
            font-size: 14px;
        }
        .test-btn:hover {
            background: #0056b3;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,123,255,0.3);
            color: white;
            text-decoration: none;
        }
        .success { background: #28a745; }
        .warning { background: #ffc107; color: #212529; }
        .danger { background: #dc3545; }
        .info { background: #17a2b8; }
        
        .status-box {
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            font-weight: bold;
        }
        .status-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status-info {
            background: #cce5ff;
            color: #004085;
            border: 1px solid #b3d7ff;
        }
        
        .dropdown-demo {
            position: relative;
            display: inline-block;
            margin: 10px;
        }
        .dropdown-toggle {
            background: #6c757d;
            color: white;
            padding: 10px 15px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
        }
        .dropdown-menu {
            display: none;
            position: absolute;
            top: 100%;
            left: 0;
            background: white;
            border: 1px solid #ddd;
            border-radius: 5px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            min-width: 150px;
            z-index: 1000;
        }
        .dropdown-menu.show {
            display: block;
        }
        .dropdown-item {
            display: block;
            padding: 8px 15px;
            color: #333;
            text-decoration: none;
            border-bottom: 1px solid #eee;
        }
        .dropdown-item:hover {
            background: #f8f9fa;
            color: #333;
            text-decoration: none;
        }
        .dropdown-item:last-child {
            border-bottom: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 导航修复工具</h1>
        
        <div class="section">
            <h3>📋 问题诊断</h3>
            <p>这个工具将帮助您诊断和修复网站导航跳转问题。</p>
            <div id="diagnosis" class="status-box status-info">
                正在检查系统状态...
            </div>
        </div>

        <div class="section">
            <h3>🔗 链接测试</h3>
            <p>点击下面的链接测试跳转功能：</p>
            <a href="index.html" class="test-btn">🏠 首页</a>
            <a href="international.html" class="test-btn">🌍 国际学生</a>
            <a href="programs.html" class="test-btn">📚 专业课程</a>
            <a href="about.html" class="test-btn">ℹ️ 关于我们</a>
            <a href="contact.html" class="test-btn">📞 联系我们</a>
            <a href="admissions.html" class="test-btn">🎓 招生信息</a>
        </div>

        <div class="section">
            <h3>📱 下拉菜单测试</h3>
            <p>测试修复后的下拉菜单功能：</p>
            <div class="dropdown-demo">
                <button class="dropdown-toggle" data-bs-toggle="dropdown">招生信息 ▼</button>
                <div class="dropdown-menu">
                    <a href="undergraduate.html" class="dropdown-item">本科招生</a>
                    <a href="graduate.html" class="dropdown-item">研究生招生</a>
                    <a href="international.html" class="dropdown-item">国际学生</a>
                    <a href="scholarships.html" class="dropdown-item">奖学金</a>
                </div>
            </div>
            <div class="dropdown-demo">
                <button class="dropdown-toggle" data-bs-toggle="dropdown">学术研究 ▼</button>
                <div class="dropdown-menu">
                    <a href="research.html" class="dropdown-item">研究项目</a>
                    <a href="faculties.html" class="dropdown-item">师资力量</a>
                    <a href="programs.html" class="dropdown-item">学术项目</a>
                </div>
            </div>
        </div>

        <div class="section">
            <h3>🛠️ 修复工具</h3>
            <button onclick="clearAllCache()" class="test-btn danger">🗑️ 清除所有缓存</button>
            <button onclick="testAllPages()" class="test-btn success">✅ 测试所有页面</button>
            <button onclick="resetNavigation()" class="test-btn warning">🔄 重置导航</button>
            <button onclick="downloadLog()" class="test-btn info">📥 下载诊断日志</button>
        </div>

        <div class="section">
            <h3>📊 实时状态</h3>
            <div id="realTimeStatus" class="status-box status-info">
                等待操作...
            </div>
        </div>
    </div>

    <script>
        let logData = [];

        // 页面加载时的诊断
        window.addEventListener('load', function() {
            diagnoseSystem();
        });

        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleString();
            logData.push(`[${timestamp}] ${type.toUpperCase()}: ${message}`);
            console.log(`[${type.toUpperCase()}] ${message}`);
        }

        function updateStatus(message, type = 'info') {
            const statusEl = document.getElementById('realTimeStatus');
            statusEl.textContent = message;
            statusEl.className = `status-box status-${type}`;
            log(message, type);
        }

        function diagnoseSystem() {
            const diagnosisEl = document.getElementById('diagnosis');
            let issues = [];
            let checks = [];

            // 检查当前URL
            checks.push(`当前URL: ${window.location.href}`);
            
            // 检查协议
            if (window.location.protocol === 'file:') {
                issues.push('⚠️ 使用file://协议，可能影响某些功能');
            }
            
            // 检查JavaScript支持
            checks.push('✅ JavaScript已启用');
            
            // 检查本地存储
            try {
                localStorage.setItem('test', 'test');
                localStorage.removeItem('test');
                checks.push('✅ 本地存储可用');
            } catch(e) {
                issues.push('❌ 本地存储不可用');
            }

            // 检查fetch API
            if (typeof fetch !== 'undefined') {
                checks.push('✅ Fetch API可用');
            } else {
                issues.push('❌ Fetch API不可用');
            }

            let result = checks.join('<br>');
            if (issues.length > 0) {
                result += '<br><br><strong>发现问题:</strong><br>' + issues.join('<br>');
                diagnosisEl.className = 'status-box status-error';
            } else {
                result += '<br><br>✅ 系统状态正常';
                diagnosisEl.className = 'status-box status-success';
            }
            
            diagnosisEl.innerHTML = result;
            log('系统诊断完成');
        }

        function clearAllCache() {
            updateStatus('正在清除缓存...', 'info');
            
            // 清除各种缓存
            if ('caches' in window) {
                caches.keys().then(names => {
                    return Promise.all(names.map(name => caches.delete(name)));
                }).then(() => {
                    log('Service Worker缓存已清除');
                });
            }
            
            localStorage.clear();
            sessionStorage.clear();
            
            // 清除cookie（当前域名）
            document.cookie.split(";").forEach(function(c) { 
                document.cookie = c.replace(/^ +/, "").replace(/=.*/, "=;expires=" + new Date().toUTCString() + ";path=/"); 
            });
            
            setTimeout(() => {
                updateStatus('缓存清除完成，建议刷新页面', 'success');
            }, 1000);
        }

        function testAllPages() {
            updateStatus('正在测试所有页面...', 'info');
            
            const pages = [
                'index.html', 'international.html', 'programs.html', 
                'about.html', 'contact.html', 'admissions.html',
                'undergraduate.html', 'graduate.html', 'scholarships.html'
            ];
            
            let results = [];
            let completed = 0;
            
            pages.forEach(page => {
                fetch(page, { method: 'HEAD' })
                    .then(response => {
                        const status = response.ok ? '✅' : '❌';
                        results.push(`${status} ${page}: ${response.status}`);
                        completed++;
                        if (completed === pages.length) {
                            showTestResults(results);
                        }
                    })
                    .catch(error => {
                        results.push(`❌ ${page}: 错误`);
                        completed++;
                        if (completed === pages.length) {
                            showTestResults(results);
                        }
                    });
            });
        }

        function showTestResults(results) {
            const hasErrors = results.some(r => r.includes('❌'));
            const message = '测试完成:<br>' + results.join('<br>');
            
            document.getElementById('realTimeStatus').innerHTML = message;
            document.getElementById('realTimeStatus').className = 
                `status-box ${hasErrors ? 'status-error' : 'status-success'}`;
        }

        function resetNavigation() {
            updateStatus('正在重置导航...', 'info');
            
            // 移除所有事件监听器（重新加载页面）
            setTimeout(() => {
                updateStatus('导航重置完成', 'success');
                if (confirm('是否要刷新页面以应用重置？')) {
                    window.location.reload(true);
                }
            }, 1000);
        }

        function downloadLog() {
            const logContent = logData.join('\n');
            const blob = new Blob([logContent], { type: 'text/plain' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `navigation-log-${new Date().toISOString().slice(0,10)}.txt`;
            a.click();
            URL.revokeObjectURL(url);
            updateStatus('诊断日志已下载', 'success');
        }

        // 简单的下拉菜单功能
        document.addEventListener('click', function(e) {
            if (e.target.classList.contains('dropdown-toggle')) {
                e.preventDefault();
                const menu = e.target.nextElementSibling;
                
                // 关闭其他菜单
                document.querySelectorAll('.dropdown-menu').forEach(m => {
                    if (m !== menu) m.classList.remove('show');
                });
                
                // 切换当前菜单
                menu.classList.toggle('show');
            } else if (!e.target.closest('.dropdown-demo')) {
                // 点击外部关闭所有菜单
                document.querySelectorAll('.dropdown-menu').forEach(m => {
                    m.classList.remove('show');
                });
            }
        });

        // 监听链接点击
        document.addEventListener('click', function(e) {
            if (e.target.tagName === 'A' && e.target.href && !e.target.href.includes('#')) {
                updateStatus(`正在跳转到: ${e.target.href}`, 'info');
            }
        });
    </script>
</body>
</html>
