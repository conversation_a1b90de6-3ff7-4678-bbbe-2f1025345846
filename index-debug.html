<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>调试版本 - 吉尔吉斯斯坦北岭大学</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="assets/css/style.css" rel="stylesheet">
    <style>
        .debug-info {
            position: fixed;
            top: 10px;
            right: 10px;
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 10px;
            border-radius: 5px;
            font-size: 12px;
            z-index: 9999;
            max-width: 300px;
        }
        .hero-buttons a {
            position: relative;
        }
        .hero-buttons a::after {
            content: attr(href);
            position: absolute;
            bottom: -20px;
            left: 0;
            font-size: 10px;
            background: rgba(255,255,255,0.9);
            color: black;
            padding: 2px 5px;
            border-radius: 3px;
            white-space: nowrap;
        }
    </style>
</head>
<body>
    <div class="debug-info" id="debugInfo">
        <div>当前幻灯片: <span id="currentSlideIndex">0</span></div>
        <div>按钮检查: <span id="buttonCheck">检查中...</span></div>
        <div>时间: <span id="debugTime"></span></div>
    </div>

    <!-- Hero Section -->
    <section class="hero-section">
        <div class="hero-slider">
            <div class="hero-slide active" style="background-image: url('assets/images/hero1.png'); background-color: #4a90e2; min-height: 500px;">
                <div class="hero-content">
                    <div class="container">
                        <div class="row">
                            <div class="col-lg-8">
                                <h1 class="hero-title" style="color: white;">知识改变命运，教育成就未来</h1>
                                <p class="hero-subtitle" style="color: white;">吉尔吉斯斯坦北岭大学致力于培养具有国际视野的优秀人才</p>
                                <div class="hero-buttons">
                                    <a href="admissions.html" class="btn btn-primary" onclick="debugClick(this, event)">了解更多</a>
                                    <a href="admissions.html" class="btn btn-outline-light" onclick="debugClick(this, event)">立即申请</a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="hero-slide" style="background-image: url('assets/images/hero2.png'); background-color: #5cb85c; min-height: 500px;">
                <div class="hero-content">
                    <div class="container">
                        <div class="row">
                            <div class="col-lg-8">
                                <h1 class="hero-title" style="color: white;">国际化教育平台</h1>
                                <p class="hero-subtitle" style="color: white;">与世界接轨的教育理念，培养全球化人才</p>
                                <div class="hero-buttons">
                                    <a href="programs.html" class="btn btn-primary" onclick="debugClick(this, event)">专业课程</a>
                                    <a href="international.html" class="btn btn-outline-light" onclick="debugClick(this, event)">国际学生</a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="hero-slide" style="background-image: url('assets/images/hero3.png'); background-color: #f0ad4e; min-height: 500px;">
                <div class="hero-content">
                    <div class="container">
                        <div class="row">
                            <div class="col-lg-8">
                                <h1 class="hero-title" style="color: white;">科研创新引领未来</h1>
                                <p class="hero-subtitle" style="color: white;">先进的科研设施，卓越的学术环境</p>
                                <div class="hero-buttons">
                                    <a href="research.html" class="btn btn-primary" onclick="debugClick(this, event)">科研成果</a>
                                    <a href="facilities.html" class="btn btn-outline-light" onclick="debugClick(this, event)">校园设施</a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="hero-nav">
            <button class="hero-prev" onclick="debugSlideChange('prev')"><i class="fas fa-chevron-left"></i></button>
            <button class="hero-next" onclick="debugSlideChange('next')"><i class="fas fa-chevron-right"></i></button>
        </div>
        <div class="hero-indicators">
            <span class="indicator active" data-slide="0" onclick="debugSlideChange(0)"></span>
            <span class="indicator" data-slide="1" onclick="debugSlideChange(1)"></span>
            <span class="indicator" data-slide="2" onclick="debugSlideChange(2)"></span>
        </div>
    </section>

    <script>
        let currentSlide = 0;
        
        function updateDebugInfo() {
            document.getElementById('currentSlideIndex').textContent = currentSlide;
            document.getElementById('debugTime').textContent = new Date().toLocaleTimeString();
            
            // 检查当前可见的按钮
            const activeSlide = document.querySelector('.hero-slide.active');
            const buttons = activeSlide ? activeSlide.querySelectorAll('.hero-buttons a') : [];
            let buttonInfo = '';
            buttons.forEach((btn, index) => {
                buttonInfo += `按钮${index + 1}: ${btn.textContent} → ${btn.href}; `;
            });
            document.getElementById('buttonCheck').textContent = buttonInfo || '无按钮';
        }
        
        function debugClick(element, event) {
            console.log('点击按钮:', element.textContent, '→', element.href);
            alert(`点击按钮: ${element.textContent}\n跳转到: ${element.href}\n\n点击确定继续跳转`);
        }
        
        function debugSlideChange(direction) {
            const slides = document.querySelectorAll('.hero-slide');
            const indicators = document.querySelectorAll('.indicator');
            
            // 移除当前活动状态
            slides[currentSlide].classList.remove('active');
            indicators[currentSlide].classList.remove('active');
            
            // 计算新的幻灯片索引
            if (direction === 'next') {
                currentSlide = (currentSlide + 1) % slides.length;
            } else if (direction === 'prev') {
                currentSlide = (currentSlide - 1 + slides.length) % slides.length;
            } else if (typeof direction === 'number') {
                currentSlide = direction;
            }
            
            // 激活新的幻灯片
            slides[currentSlide].classList.add('active');
            indicators[currentSlide].classList.add('active');
            
            updateDebugInfo();
        }
        
        // 初始化
        updateDebugInfo();
        setInterval(updateDebugInfo, 1000);
        
        // 页面加载完成后的检查
        window.onload = function() {
            console.log('调试页面加载完成');
            updateDebugInfo();
        };
    </script>
</body>
</html>
