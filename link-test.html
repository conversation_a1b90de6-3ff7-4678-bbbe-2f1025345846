<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>链接测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-link {
            display: inline-block;
            margin: 10px;
            padding: 12px 24px;
            background-color: #007bff;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            transition: background-color 0.3s;
        }
        .test-link:hover {
            background-color: #0056b3;
            color: white;
            text-decoration: none;
        }
        .status {
            margin-top: 20px;
            padding: 15px;
            border-radius: 5px;
            background-color: #e9ecef;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>链接跳转测试</h1>
        <p>点击下面的链接测试跳转功能：</p>
        
        <div class="links-section">
            <h3>主要页面链接：</h3>
            <a href="index.html" class="test-link">首页</a>
            <a href="international.html" class="test-link">国际学生</a>
            <a href="programs.html" class="test-link">专业课程</a>
            <a href="admissions.html" class="test-link">招生信息</a>
            <a href="about.html" class="test-link">关于我们</a>
            <a href="contact.html" class="test-link">联系我们</a>
        </div>

        <div class="links-section">
            <h3>相对路径测试：</h3>
            <a href="./international.html" class="test-link">./国际学生</a>
            <a href="/international.html" class="test-link">/国际学生</a>
            <a href="international.html?test=1" class="test-link">国际学生(带参数)</a>
        </div>

        <div class="status" id="status">
            <strong>状态：</strong>等待测试...
        </div>

        <div style="margin-top: 20px;">
            <h3>调试信息：</h3>
            <p><strong>当前URL：</strong> <span id="currentUrl"></span></p>
            <p><strong>Base URL：</strong> <span id="baseUrl"></span></p>
            <p><strong>协议：</strong> <span id="protocol"></span></p>
        </div>

        <div style="margin-top: 20px;">
            <button onclick="testLinks()" class="test-link" style="background-color: #28a745;">测试所有链接</button>
            <button onclick="clearCache()" class="test-link" style="background-color: #dc3545;">清除缓存</button>
        </div>
    </div>

    <script>
        // 显示当前页面信息
        document.getElementById('currentUrl').textContent = window.location.href;
        document.getElementById('baseUrl').textContent = window.location.origin + window.location.pathname.substring(0, window.location.pathname.lastIndexOf('/') + 1);
        document.getElementById('protocol').textContent = window.location.protocol;

        // 测试链接函数
        function testLinks() {
            const status = document.getElementById('status');
            status.innerHTML = '<strong>状态：</strong>正在测试链接...';
            status.className = 'status';

            const links = [
                'index.html',
                'international.html',
                'programs.html',
                'admissions.html',
                'about.html',
                'contact.html'
            ];

            let results = [];
            let completed = 0;

            links.forEach(link => {
                fetch(link, { method: 'HEAD' })
                    .then(response => {
                        results.push(`${link}: ${response.status} ${response.statusText}`);
                        completed++;
                        if (completed === links.length) {
                            displayResults(results);
                        }
                    })
                    .catch(error => {
                        results.push(`${link}: 错误 - ${error.message}`);
                        completed++;
                        if (completed === links.length) {
                            displayResults(results);
                        }
                    });
            });
        }

        function displayResults(results) {
            const status = document.getElementById('status');
            const hasErrors = results.some(result => result.includes('错误') || result.includes('404'));
            
            status.className = hasErrors ? 'status error' : 'status success';
            status.innerHTML = '<strong>测试结果：</strong><br>' + results.join('<br>');
        }

        // 清除缓存函数
        function clearCache() {
            if ('caches' in window) {
                caches.keys().then(names => {
                    names.forEach(name => {
                        caches.delete(name);
                    });
                });
            }
            
            // 清除本地存储
            localStorage.clear();
            sessionStorage.clear();
            
            // 强制刷新
            window.location.reload(true);
        }

        // 监听链接点击事件
        document.addEventListener('click', function(e) {
            if (e.target.classList.contains('test-link') && e.target.href) {
                const status = document.getElementById('status');
                status.innerHTML = `<strong>状态：</strong>正在跳转到 ${e.target.href}...`;
                status.className = 'status';
            }
        });

        // 检查页面加载错误
        window.addEventListener('error', function(e) {
            console.error('页面错误:', e);
            const status = document.getElementById('status');
            status.innerHTML = `<strong>错误：</strong>${e.message}`;
            status.className = 'status error';
        });
    </script>
</body>
</html>
