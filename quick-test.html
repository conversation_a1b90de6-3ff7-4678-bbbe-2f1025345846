<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>快速跳转测试</title>
    <link rel="stylesheet" href="assets/css/bootstrap.min.css">
    <link rel="stylesheet" href="assets/css/fontawesome.min.css">
    <link rel="stylesheet" href="assets/css/style.css">
    <style>
        body {
            padding-top: 100px;
            background-color: #f8f9fa;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            padding: 30px;
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-link {
            display: inline-block;
            margin: 10px;
            padding: 12px 24px;
            background-color: #007bff;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            transition: all 0.3s;
        }
        .test-link:hover {
            background-color: #0056b3;
            color: white;
            text-decoration: none;
            transform: translateY(-2px);
        }
        .status {
            margin-top: 20px;
            padding: 15px;
            border-radius: 5px;
            background-color: #e9ecef;
        }
        .nav-test {
            background: #2c3e50;
            padding: 10px 0;
            margin-bottom: 30px;
        }
    </style>
</head>
<body>
    <!-- 简化的导航栏测试 -->
    <nav class="nav-test">
        <div class="container">
            <div class="dropdown d-inline-block">
                <a class="btn btn-secondary dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                    招生信息
                </a>
                <ul class="dropdown-menu">
                    <li><a class="dropdown-item" href="undergraduate.html">本科招生</a></li>
                    <li><a class="dropdown-item" href="graduate.html">研究生招生</a></li>
                    <li><a class="dropdown-item" href="international.html">国际学生</a></li>
                    <li><a class="dropdown-item" href="scholarships.html">奖学金</a></li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="test-container">
        <h1>跳转测试页面</h1>
        <p>测试修复后的链接跳转功能：</p>
        
        <div class="mb-4">
            <h3>直接链接测试：</h3>
            <a href="international.html" class="test-link">国际学生页面</a>
            <a href="programs.html" class="test-link">专业课程</a>
            <a href="about.html" class="test-link">关于我们</a>
            <a href="contact.html" class="test-link">联系我们</a>
        </div>

        <div class="mb-4">
            <h3>下拉菜单测试：</h3>
            <p>点击上方导航栏中的"招生信息"下拉菜单，然后点击"国际学生"链接。</p>
        </div>

        <div class="status" id="status">
            <strong>状态：</strong>等待测试...
        </div>

        <div class="mt-4">
            <h3>调试信息：</h3>
            <p><strong>当前URL：</strong> <span id="currentUrl"></span></p>
            <p><strong>用户代理：</strong> <span id="userAgent"></span></p>
        </div>

        <div class="mt-4">
            <button onclick="testAllLinks()" class="test-link" style="background-color: #28a745;">测试所有链接</button>
            <button onclick="window.location.reload(true)" class="test-link" style="background-color: #dc3545;">强制刷新</button>
        </div>
    </div>

    <!-- 引入 JavaScript -->
    <script src="assets/js/bootstrap.bundle.min.js"></script>
    <script src="assets/js/main.js"></script>
    
    <script>
        // 显示调试信息
        document.getElementById('currentUrl').textContent = window.location.href;
        document.getElementById('userAgent').textContent = navigator.userAgent;

        // 测试所有链接
        function testAllLinks() {
            const status = document.getElementById('status');
            status.innerHTML = '<strong>状态：</strong>正在测试链接...';

            const links = [
                'international.html',
                'programs.html', 
                'about.html',
                'contact.html',
                'undergraduate.html',
                'graduate.html',
                'scholarships.html'
            ];

            let results = [];
            let completed = 0;

            links.forEach(link => {
                fetch(link, { method: 'HEAD' })
                    .then(response => {
                        const statusText = response.ok ? '✅ 正常' : '❌ 错误';
                        results.push(`${link}: ${response.status} ${statusText}`);
                        completed++;
                        if (completed === links.length) {
                            displayResults(results);
                        }
                    })
                    .catch(error => {
                        results.push(`${link}: ❌ 网络错误 - ${error.message}`);
                        completed++;
                        if (completed === links.length) {
                            displayResults(results);
                        }
                    });
            });
        }

        function displayResults(results) {
            const status = document.getElementById('status');
            const hasErrors = results.some(result => result.includes('❌'));
            
            status.style.backgroundColor = hasErrors ? '#f8d7da' : '#d4edda';
            status.style.color = hasErrors ? '#721c24' : '#155724';
            status.innerHTML = '<strong>测试结果：</strong><br>' + results.join('<br>');
        }

        // 监听所有链接点击
        document.addEventListener('click', function(e) {
            if (e.target.tagName === 'A' && e.target.href && !e.target.href.includes('#')) {
                const status = document.getElementById('status');
                status.innerHTML = `<strong>状态：</strong>正在跳转到 ${e.target.href}...`;
                status.style.backgroundColor = '#cce5ff';
                status.style.color = '#004085';
            }
        });

        // 检查页面加载错误
        window.addEventListener('error', function(e) {
            console.error('页面错误:', e);
            const status = document.getElementById('status');
            status.innerHTML = `<strong>错误：</strong>${e.message}`;
            status.style.backgroundColor = '#f8d7da';
            status.style.color = '#721c24';
        });

        // 页面加载完成后的状态
        window.addEventListener('load', function() {
            const status = document.getElementById('status');
            status.innerHTML = '<strong>状态：</strong>页面加载完成，可以开始测试链接跳转。';
            status.style.backgroundColor = '#d4edda';
            status.style.color = '#155724';
        });
    </script>
</body>
</html>
