<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>简单按钮测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .btn {
            display: inline-block;
            padding: 12px 24px;
            margin: 5px;
            text-decoration: none;
            border-radius: 5px;
            font-weight: bold;
            transition: all 0.3s ease;
        }
        .btn-primary {
            background: #007bff;
            color: white;
        }
        .btn-primary:hover {
            background: #0056b3;
        }
        .btn-outline {
            background: transparent;
            color: #007bff;
            border: 2px solid #007bff;
        }
        .btn-outline:hover {
            background: #007bff;
            color: white;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            font-weight: bold;
        }
        .success { background: #d4edda; color: #155724; }
        .info { background: #d1ecf1; color: #0c5460; }
    </style>
</head>
<body>
    <h1>按钮跳转测试</h1>
    
    <div class="test-section">
        <h2>第一个轮播图按钮（应该跳转到招生信息）</h2>
        <a href="admissions.html" class="btn btn-primary" onclick="logClick(this, event)">了解更多</a>
        <a href="admissions.html" class="btn btn-outline" onclick="logClick(this, event)">立即申请</a>
    </div>
    
    <div class="test-section">
        <h2>第二个轮播图按钮</h2>
        <a href="programs.html" class="btn btn-primary" onclick="logClick(this, event)">专业课程</a>
        <a href="international.html" class="btn btn-outline" onclick="logClick(this, event)">国际学生</a>
    </div>
    
    <div class="test-section">
        <h2>第三个轮播图按钮（参考）</h2>
        <a href="research.html" class="btn btn-primary" onclick="logClick(this, event)">科研成果</a>
        <a href="facilities.html" class="btn btn-outline" onclick="logClick(this, event)">校园设施</a>
    </div>
    
    <div class="test-section">
        <h2>点击日志</h2>
        <div id="clickLog"></div>
        <button onclick="clearLog()" style="padding: 8px 16px; background: #dc3545; color: white; border: none; border-radius: 4px; cursor: pointer;">清除日志</button>
    </div>
    
    <div class="test-section">
        <h2>页面状态</h2>
        <div class="status info">
            当前时间: <span id="currentTime"></span><br>
            页面URL: <span id="currentUrl"></span>
        </div>
    </div>
    
    <script>
        function updateTime() {
            document.getElementById('currentTime').textContent = new Date().toLocaleString();
            document.getElementById('currentUrl').textContent = window.location.href;
        }
        
        function logClick(element, event) {
            const log = document.getElementById('clickLog');
            const time = new Date().toLocaleTimeString();
            const text = element.textContent.trim();
            const href = element.href;
            
            log.innerHTML += `<div class="status success">
                [${time}] 点击按钮: "${text}" → ${href}
            </div>`;
            
            // 让用户看到日志，然后继续跳转
            console.log(`点击按钮: ${text} → ${href}`);
        }
        
        function clearLog() {
            document.getElementById('clickLog').innerHTML = '';
        }
        
        // 初始化
        updateTime();
        setInterval(updateTime, 1000);
        
        // 检查页面是否正确加载
        window.onload = function() {
            console.log('页面加载完成');
            const log = document.getElementById('clickLog');
            log.innerHTML = '<div class="status info">页面加载完成，可以开始测试按钮</div>';
        };
    </script>
</body>
</html>
