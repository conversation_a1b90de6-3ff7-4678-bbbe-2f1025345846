<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>按钮测试</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <h1>按钮跳转测试</h1>
        
        <div class="row mt-4">
            <div class="col-md-6">
                <h3>第一个轮播图按钮</h3>
                <div class="mb-3">
                    <a href="admissions.html" class="btn btn-primary">了解更多</a>
                    <a href="admissions.html" class="btn btn-outline-primary">立即申请</a>
                </div>
            </div>
            
            <div class="col-md-6">
                <h3>第二个轮播图按钮</h3>
                <div class="mb-3">
                    <a href="programs.html" class="btn btn-primary">专业课程</a>
                    <a href="international.html" class="btn btn-outline-primary">国际学生</a>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <h3>测试所有相关页面链接</h3>
                <div class="mb-3">
                    <a href="admissions.html" class="btn btn-success me-2">招生信息</a>
                    <a href="programs.html" class="btn btn-success me-2">专业课程</a>
                    <a href="international.html" class="btn btn-success me-2">国际学生</a>
                    <a href="facilities.html" class="btn btn-warning me-2">校园设施</a>
                    <a href="about.html" class="btn btn-info me-2">关于我们</a>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <h3>当前页面信息</h3>
                <p>当前页面URL: <span id="currentUrl"></span></p>
                <p>点击按钮后会跳转到对应页面</p>
            </div>
        </div>
    </div>
    
    <script>
        document.getElementById('currentUrl').textContent = window.location.href;
        
        // 添加点击事件监听器来调试
        document.querySelectorAll('a').forEach(link => {
            link.addEventListener('click', function(e) {
                console.log('点击链接:', this.href);
                console.log('目标页面:', this.getAttribute('href'));
            });
        });
    </script>
</body>
</html>
